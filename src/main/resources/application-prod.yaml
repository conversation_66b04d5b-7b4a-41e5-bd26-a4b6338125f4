spring:
  datasource:
    primary:
      jdbc-url: ${db.fms.url}
      username: ${db.fms.username}
      password: ${db.fms.password}
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    second:
      jdbc-url: ${db.fleetmonitor.url}
      username: ${db.fleetmonitor.username}
      password: ${db.fleetmonitor.password}
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    third:
      jdbc-url: ${db.product.url}
      username: ${db.product.username}
      password: ${db.product.password}
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml

authorize:
  jwt:
    secret: "SP&v0P^jV3^b22Fg@DkY*8"
    expire-seconds: 259200

redback:
  baseUrl: https://api1.redbacktech.com
  environment: Production
  serviceAccountName: <EMAIL>
  serviceAccountPassword: ${redback.serviceAccountPassword}

tuya:
  accessKey: ${tuya.accessKey}
  secretKey: ${tuya.secretKey}
  hostName: ${tuya.hostName}

azure:
  iot:
    hub:
      connectionString: ${azure.iot.hub.connectionString}
      hostName: RBProductionIOTHub.azure-devices.net
  storage:
    oldAccountName: rbproductionstorage
    oldAccountKey: ${azure.storage.oldAccountKey}
    telemetryAccountName: rbprodtelemetry
    telemetryAccountKey: ${azure.storage.telemetryAccountKey}
    enableNewTelemetryStorage: false
    useOnlyNewTelemetryFromDate: ********
    useOnlyOldStoragePriorToDate: ********

aliyun:
  tablestore:
    endPoint: ${aliyun.tablestore.endPoint}
    accessKey: ${aliyun.tablestore.accessKey}
    secretKey: ${aliyun.tablestore.secretKey}
    instanceName: tuya-prod
    telemetryStorageFromDateyyyyMMdd: ********
    telemetryOldStoragePriorToDateyyyyMMdd: ********

EMSStorageContainerURL: https://rbprodstorage.blob.core.windows.net/emsfirmwares/

directMethodChangeHostnamePasswords: ${directMethodChangeHostnamePasswords}

portal:
  url: https://portal.redbacktech.com
  redbackBatterySwitch: false
  exportLimitMessageThresholdW: 200
  numberOfConsecutiveDaysBatteriesMismatchedToDisabled: 14
  batteryFullThreshold: 0.95
  maxDaysDateRange: 90
  listOfStorageReductionJobs: ThinAzureStorage01
  siteChartMaxLookAheadAllowanceInSec: 180

forecast:
  url: http://redback-forecast.tuyaprod:8000
  
email:
  fromAddress: <EMAIL>
  networkCredentialUserID: <EMAIL>
  networkCredentialPwd: f398i821ZIk5vT1z
  alertNetworkCredentialUserID: <EMAIL>
  alertNetworkCredentialPwd: f398i821ZIk5vT1z
  smtpHost: smtp.larksuite.com
  smtpPort : 587

findPasswordLink: https://fms.redbacktech.com/auth/resetPassword


push:
  task:
    enabled: true                    # 是否启用推送功能
    scheduled-interval: 5000         # 定时扫描间隔（毫秒）
    max-retry-count: 3               # 最大重试次数
    retry-delay-seconds: 30          # 重试延迟（秒）
    production: true                # 是否为生产环境