package com.ebon.energy.fms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 推送任务配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "push.task")
public class PushTaskConfig {

    /**
     * 定时扫描间隔（毫秒），默认5秒
     */
    private Long scheduledInterval = 5000L;

    /**
     * 最大重试次数，默认3次
     */
    private Integer maxRetryCount = 3;



    /**
     * 是否启用推送功能，默认启用
     */
    private Boolean enabled = true;

    /**
     * 推送失败时的重试延迟（秒），默认30秒
     */
    private Integer retryDelaySeconds = 30;


    private Boolean production = false;
}
