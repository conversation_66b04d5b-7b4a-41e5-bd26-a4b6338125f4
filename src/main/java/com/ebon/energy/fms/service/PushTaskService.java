package com.ebon.energy.fms.service;

import cn.jiguang.sdk.bean.push.PushSendParam;
import cn.jiguang.sdk.bean.push.PushSendResult;
import cn.jiguang.sdk.bean.push.audience.Audience;
import cn.jiguang.sdk.bean.push.message.notification.NotificationMessage;
import cn.jiguang.sdk.bean.push.options.Options;
import cn.jiguang.sdk.constants.ApiConstants;
import cn.jiguang.sdk.enums.platform.Platform;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.config.PushTaskConfig;
import com.ebon.energy.fms.domain.entity.PushTaskDO;
import com.ebon.energy.fms.domain.po.CreatePushTaskPO;
import com.ebon.energy.fms.domain.po.PushTaskQueryPO;
import com.ebon.energy.fms.domain.po.UpdatePushTaskPO;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.PushTaskVO;
import com.ebon.energy.fms.domain.vo.PushTaskStatisticsVO;
import com.ebon.energy.fms.mapper.primary.PushTaskMapper;
import com.ebon.energy.fms.remote.JiGuangRemoteService;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 推送任务服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PushTaskService {

    private final PushTaskMapper pushTaskMapper;
    private final JiGuangRemoteService jiGuangRemoteService;
    private final PushTaskConfig pushTaskConfig;



    /**
     * 创建推送任务
     *
     * @param po 创建请求
     * @return 任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createPushTask(CreatePushTaskPO po) {
        log.info("创建推送任务，参数: {}", po);

        // 构建任务实体
        PushTaskDO task = buildPushTask(po);
        
        // 保存任务
        pushTaskMapper.insert(task);
        
        // 如果是立即发送，直接执行推送
        if (isImmediatePush(po.getPushTime())) {
            log.info("立即发送推送任务，ID: {}", task.getId());
            executePushTask(task);
        }
        
        return task.getId();
    }

    /**
     * 分页查询推送任务
     *
     * @param po 查询参数
     * @return 分页结果
     */
    public PageResult<PushTaskVO> queryPushTasks(PushTaskQueryPO po) {
        log.info("分页查询推送任务，参数: {}", po);

        Page<PushTaskDO> page = new Page<>(po.getCurrent(), po.getPageSize());
        IPage<PushTaskDO> result = pushTaskMapper.selectPageWithConditions(
                page, po.getOs(), po.getStatus(), po.getKeyword());

        List<PushTaskVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.of(result.getCurrent(), result.getSize(), result.getTotal(), voList);
    }

    /**
     * 更新推送任务（仅限PENDING状态）
     *
     * @param po 更新请求
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePushTask(UpdatePushTaskPO po) {
        log.info("更新推送任务，参数: {}", po);

        // 查询现有任务
        PushTaskDO existingTask = pushTaskMapper.selectById(po.getId());
        if (existingTask == null) {
            throw new BizException("推送任务不存在");
        }

        // 只允许更新PENDING状态的任务
        if (!PushTaskDO.Status.PENDING.equals(existingTask.getStatus())) {
            throw new BizException("只能编辑待发送状态的任务");
        }

        // 更新任务信息
        PushTaskDO updateTask = new PushTaskDO()
                .setId(po.getId())
                .setOs(po.getOs())
                .setVersionList(String.join(",", po.getVersionList()))
                .setTitle(po.getTitle())
                .setContent(po.getContent())
                .setPushTime(isImmediatePush(po.getPushTime()) ? System.currentTimeMillis() : po.getPushTime())
                .setUpdatedAt(System.currentTimeMillis())
                .setUpdatedBy(RequestUtil.getCurrentUserName());

        pushTaskMapper.updateById(updateTask);

        // 如果改为立即发送，直接执行推送
        if (isImmediatePush(po.getPushTime())) {
            log.info("更新为立即发送，执行推送任务，ID: {}", po.getId());
            PushTaskDO taskToExecute = pushTaskMapper.selectById(po.getId());
            executePushTask(taskToExecute);
        }
    }

    /**
     * 根据ID查询推送任务
     *
     * @param id 任务ID
     * @return 任务详情
     */
    public PushTaskVO getPushTaskById(Long id) {
        PushTaskDO task = pushTaskMapper.selectById(id);
        if (task == null) {
            throw new BizException("推送任务不存在");
        }
        return convertToVO(task);
    }

    /**
     * 定时扫描并执行待发送任务
     * 扫描间隔由配置决定，默认每5秒执行一次
     */
    @Scheduled(fixedRateString = "#{@pushTaskConfig.scheduledInterval}")
    public void scheduledPushTasks() {
        // 检查推送功能是否启用
        if (!pushTaskConfig.getEnabled()) {
            return;
        }

        try {
            long currentTime = System.currentTimeMillis();
            List<PushTaskDO> pendingTasks = pushTaskMapper.selectPendingTasks(currentTime);

            if (!CollectionUtils.isEmpty(pendingTasks)) {
                log.info("发现 {} 个待发送推送任务", pendingTasks.size());

                for (PushTaskDO task : pendingTasks) {
                    try {
                        executePushTask(task);
                    } catch (Exception e) {
                        log.error("执行推送任务失败，任务ID: {}", task.getId(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("定时扫描推送任务失败", e);
        }
    }

    /**
     * 执行推送任务
     *
     * @param task 推送任务
     */
    private void executePushTask(PushTaskDO task) {
        log.info("开始执行推送任务，ID: {}, 标题: {}", task.getId(), task.getTitle());

        try {
            // 构建推送参数
            PushSendParam pushParam = buildPushSendParam(task);

            // 调用极光推送
            PushSendResult result = jiGuangRemoteService.pushMessage(pushParam);

            // 更新任务状态为成功
            updateTaskStatus(task.getId(), PushTaskDO.Status.SENT, task.getRetryCount());

            log.info("推送任务执行成功，ID: {}, 结果: {}", task.getId(), result);

        } catch (Exception e) {
            log.error("推送任务执行失败，ID: {}", task.getId(), e);

            // 处理失败重试
            handlePushFailure(task, e);
        }
    }

    /**
     * 处理推送失败
     *
     * @param task 推送任务
     * @param exception 异常信息
     */
    private void handlePushFailure(PushTaskDO task, Exception exception) {
        int newRetryCount = task.getRetryCount() + 1;

        if (newRetryCount >= pushTaskConfig.getMaxRetryCount()) {
            // 超过最大重试次数，标记为失败
            updateTaskStatus(task.getId(), PushTaskDO.Status.FAILED, newRetryCount);
            log.error("推送任务达到最大重试次数，标记为失败，ID: {}", task.getId());
        } else {
            // 增加重试次数，保持PENDING状态
            updateTaskStatus(task.getId(), PushTaskDO.Status.PENDING, newRetryCount);
            log.warn("推送任务失败，将进行第 {} 次重试，ID: {}", newRetryCount, task.getId());
        }
    }

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @param retryCount 重试次数
     */
    private void updateTaskStatus(Long taskId, String status, Integer retryCount) {
        pushTaskMapper.updateTaskStatus(
                taskId, 
                status, 
                retryCount, 
                System.currentTimeMillis(), 
                "SYSTEM"
        );
    }

    /**
     * 构建推送任务实体
     *
     * @param po 创建请求
     * @return 任务实体
     */
    private PushTaskDO buildPushTask(CreatePushTaskPO po) {
        long currentTime = System.currentTimeMillis();
        String currentUser = RequestUtil.getCurrentUserName();
        
        return new PushTaskDO()
                .setOs(po.getOs())
                .setVersionList(String.join(",", po.getVersionList()))
                .setTitle(po.getTitle())
                .setContent(po.getContent())
                .setPushTime(isImmediatePush(po.getPushTime()) ? currentTime : po.getPushTime())
                .setStatus(PushTaskDO.Status.PENDING)
                .setRetryCount(0)
                .setCreatedAt(currentTime)
                .setUpdatedAt(currentTime)
                .setCreatedBy(currentUser)
                .setUpdatedBy(currentUser);
    }

    /**
     * 构建极光推送参数
     *
     * @param task 推送任务
     * @return 推送参数
     */
    private PushSendParam buildPushSendParam(PushTaskDO task) {
        PushSendParam param = new PushSendParam();
        
        // 设置推送平台
        param.setPlatform(buildPlatformList(task.getOs()));
        
        // 设置推送目标（全部用户）
        param.setAudience(ApiConstants.Audience.ALL);
        
        // 设置通知内容
        param.setNotification(buildNotificationMessage(task));
        var options = new Options();
        options.setApnsProduction(pushTaskConfig.getProduction());
        param.setOptions(options);
        return param;
    }

    /**
     * 构建平台列表
     *
     * @param os 操作系统
     * @return 平台列表
     */
    private List<Platform> buildPlatformList(String os) {
        switch (os) {
            case PushTaskDO.OS.IOS:
                return Arrays.asList(Platform.ios);
            case PushTaskDO.OS.ANDROID:
                return Arrays.asList(Platform.android);
            case PushTaskDO.OS.ALL:
            default:
                return Arrays.asList(Platform.android, Platform.ios);
        }
    }

    /**
     * 构建通知消息
     *
     * @param task 推送任务
     * @return 通知消息
     */
    private NotificationMessage buildNotificationMessage(PushTaskDO task) {
        NotificationMessage notification = new NotificationMessage();
        notification.setAlert(task.getContent());
        
        // Android通知
        NotificationMessage.Android android = new NotificationMessage.Android();
        android.setAlert(task.getContent());
        android.setTitle(task.getTitle());
        notification.setAndroid(android);
        
        // iOS通知
        NotificationMessage.IOS ios = new NotificationMessage.IOS();
        Map<String, String> iOSAlert = new HashMap<>();
        iOSAlert.put("title", task.getTitle());
        iOSAlert.put("body", task.getContent());
        ios.setAlert(iOSAlert);
        notification.setIos(ios);
        
        return notification;
    }

    /**
     * 转换为VO对象
     *
     * @param task 任务实体
     * @return VO对象
     */
    private PushTaskVO convertToVO(PushTaskDO task) {
        PushTaskVO vo = new PushTaskVO();
        vo.setId(task.getId());
        vo.setOs(task.getOs());
        vo.setVersionList(StringUtils.hasText(task.getVersionList()) ? 
                Arrays.asList(task.getVersionList().split(",")) : null);
        vo.setTitle(task.getTitle());
        vo.setContent(task.getContent());
        vo.setPushTime(task.getPushTime());
        vo.setStatus(task.getStatus());
        vo.setRetryCount(task.getRetryCount());
        vo.setCreatedAt(task.getCreatedAt());
        vo.setUpdatedAt(task.getUpdatedAt());
        vo.setCreatedBy(task.getCreatedBy());
        vo.setUpdatedBy(task.getUpdatedBy());
        return vo;
    }

    /**
     * 获取推送任务统计信息
     *
     * @return 统计信息
     */
    public PushTaskStatisticsVO getStatistics() {
        PushTaskStatisticsVO statistics = new PushTaskStatisticsVO();

        // 基础统计
        statistics.setTotalTasks(pushTaskMapper.countTotal());
        statistics.setPendingTasks(pushTaskMapper.countByStatus(PushTaskDO.Status.PENDING));
        statistics.setSentTasks(pushTaskMapper.countByStatus(PushTaskDO.Status.SENT));
        statistics.setFailedTasks(pushTaskMapper.countByStatus(PushTaskDO.Status.FAILED));

        // 今日统计
        LocalDate today = LocalDate.now();
        long startOfDay = today.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        long endOfDay = today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        statistics.setTodayCreatedTasks(pushTaskMapper.countTodayCreated(startOfDay, endOfDay));
        statistics.setTodaySentTasks(pushTaskMapper.countTodaySent(startOfDay, endOfDay));

        // 成功率计算
        long totalCompleted = statistics.getSentTasks() + statistics.getFailedTasks();
        if (totalCompleted > 0) {
            statistics.setSuccessRate((double) statistics.getSentTasks() / totalCompleted * 100);
        } else {
            statistics.setSuccessRate(0.0);
        }

        // 平均重试次数
        Double avgRetryCount = pushTaskMapper.calculateAverageRetryCount();
        statistics.setAverageRetryCount(avgRetryCount != null ? avgRetryCount : 0.0);

        return statistics;
    }

    /**
     * 判断是否立即推送
     *
     * @param pushTime 推送时间
     * @return 是否立即推送
     */
    private boolean isImmediatePush(Long pushTime) {
        return pushTime == null || pushTime <= 0 || pushTime <= System.currentTimeMillis();
    }
}
