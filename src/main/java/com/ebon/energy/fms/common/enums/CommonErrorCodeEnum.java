package com.ebon.energy.fms.common.enums;

public enum CommonErrorCodeEnum implements BaseErrorCodeEnum {
    SUCCESS("0", "Success"),
    ERROR("1", "internal error"),
    USER_NOT_EXIST("2", "user not existed"),
    LOGIN_TOKEN_ERROR("3", "need login"),
    PASSWORD_ERROR("4", "password error"),
    ROLE_NOT_EXIST("5", "Role not existed"),
    ROLE_UNAVAILABLE("6", "Role unavailable"),
    USER_ALREADY_EXISTS("7", "User already exists"),
    ROLE_ALREADY_EXISTS("8", "Role already exists"),
    ROLE_CANNOT_BE_DISABLED("9", "The role is currently in use and cannot be disabled"),
    USER_DISABLED("10", "User is disabled"),
    BAD_REQUEST("400", "Bad Request"),
    UNAUTHORIZED("401", "Unauthorized"),
    FORBIDDEN("403", "Forbidden"),
    NOT_FOUND("404", "Not Found"),
    METHOD_NOT_ALLOWED("405", "Method Not Allowed"),
    NOT_ACCEPTABLE("406", "Not Acceptable"),
    PROXY_AUTHENTICATION_REQUIRED("407", "Proxy Authentication Required"),
    REQUEST_TIMEOUT("408", "Request Timeout"),
    LENGTH_REQUIRED("411", "Length Required"),
    TOO_MANY_REQUESTS("429", "Too Many Requests"),
    REQUEST_HEADER_FIELDS_TOO_LARGE("431", "Request Header Fields Too Large"),
    UNAVAILABLE_FOR_LEGAL_REASONS("451", "Unavailable For Legal Reasons"),
    INTERNAL_SERVER_ERROR("500", "Internal Server Error"),
    NOT_IMPLEMENTED("501", "Not Implemented"),
    BAD_GATEWAY("502", "Bad Gateway"),
    SERVICE_UNAVAILABLE("503", "Service Unavailable"),
    GATEWAY_TIMEOUT("504", "Gateway Timeout"),
    HTTP_VERSION_NOT_SUPPORTED("505", "HTTP Version not supported"),
    INSUFFICIENT_STORAGE("507", "Insufficient Storage"),
    LOOP_DETECTED("508", "Loop Detected"),
    NOT_EXTENDED("510", "Not Extended"),
    NETWORK_AUTHENTICATION_REQUIRED("511", "Network Authentication Required"),
    REQUEST_ERROR("1000", "Request error"),
    ERR_REQUEST_PARAMETER("1002", "Request parameter error"),
    REQUEST_PARAMETER_VALIDATE_FAIL("1007", "Request parameter validation failed"),
    PARAM_START_TIME_NULL("1008", "Request parameter startTime is null"),
    PARAM_END_TIME_NULL("1009", "Request parameter endTime is null"),
    SUBMIT_REPEATED("1010", "Submit repeated"),
    SYSTEM_UNKNOWN_ERROR("100000", "System Unknown Error"),
    CLIENT_HTTP_METHOD_ERROR("100001", "Http client request method error"),
    CLIENT_REQUEST_BODY_CHECK_ERROR("100002", "Client request method body argument not valid"),
    CLIENT_REQUEST_BODY_FORMAT_ERROR("100003", "Client request body JSON malformed or field type mismatch"),
    CLIENT_PATH_VARIABLE_ERROR("100004", "Client url's parameter type wrong"),
    CLIENT_REQUEST_PARAM_CHECK_ERROR("100005", "The client request parameter verification failed"),
    CLIENT_REQUEST_PARAM_REQUIRED_ERROR("100006", "The client request is missing a required parameter"),
    SERVER_ILLEGAL_ARGUMENT_ERROR("100007", "Business method parameter check failed"),
    ENUM_PARAM_MUST_BE_IN_ALLOWABLE_VALUE("100008", "The value of [{0}] must be {1}"),
    SQL_ERROR("100009", "SQL execution exception"),
    SEND_MQ_FAILED("100010", "Failed to send MQ message"),
    SQL_ERROR_DUPLICATE_INDEX("100011", "Data already exists"),
    REQUEST_TOO_MANY("100012", "Request is too frequent. Please try again later"),
    BLOCK_FLOW_ERROR("100013", "API rate limit exceeded"),
    BLOCK_DEGRADE_ERROR("100014", "Service degradation triggered"),
    BLOCK_AUTHORITY_ERROR("100015", "Authorization rule failed"),
    START_TIME_MORE_THAN_END_TIME_ERROR("100016", "End time cannot be earlier than start time"),
    TIME_DURATION_MORE_THAN_3MONTHS_ERROR("100017", "Order time duration more than 3 months"),
    SYSTEM_PROTECTION_TRIGGERED("100018", "System protection rule triggered"),
    PARAM_FLOW_LIMITED("100019", "Hot parameter rate limit exceeded"),
    PARAM_INVALID_ERROR("103000", "Parameter is invalid"),
    SIGN_NO_APPID("103001", "Appid is null"),
    SIGN_NO_APPSERET("103002", "AppSecret is null"),
    SIGN_NO_TIMESTAMP("103003", "Timestamp is null"),
    SIGN_NO_SIGN("103004", "Signature content is null"),
    SIGN_TIMESTAMP_INVALID("103005", "Request timestamp is expired"),
    SIGN_DUPLICATION("103006", "Signature requests repeatedly"),
    SIGN_VERIFY_FAIL("103007", "Signature verification is failed"),
    RESEND_SIGN_FAIL("103008", "Signature resend is failed"),
    SIGN_ENVELOPE_NOT_EXIST("103009", "Signature envelope does not exist"),
    ENVELOP_ID_NULL("103010", "EnvelopId is null"),
    ERROR_TIME_MUCH("103011", "Too many errors occurred"),
    IDENTITY_FIRST("103012", "Please verify your identity first"),
    UNSUPPORTED_SIGN_METHOD("103013", "Signature method is not supported"),
    SIGN_APPSERET_INVALID("103014", "Signature appsecret is invalid"),
    SIGN_CONFIGURATION_NOT_FOUND("103015", "Signature configuration is not found"),
    APP_VERSION_EXIST("103016", "App Version Management already exists"),
    APP_VERSION_NOT_EXIST("103017", "App Version Management does not exist"),
    FIND_PASSWORD_LINK_HAS_EXPIRED("103018", "Link has expired, please try again"), 
    PUSH_API_ERROR("103019", "push service error");

    private String errorCode;
    private String errorMsg;

    private CommonErrorCodeEnum(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return this.errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public static void main(String[] args) {
        CommonErrorCodeEnum[] commonErrorCodeEnum = values();
        CommonErrorCodeEnum[] var2 = commonErrorCodeEnum;
        int var3 = commonErrorCodeEnum.length;

        for (int var4 = 0; var4 < var3; ++var4) {
            CommonErrorCodeEnum errorCodeEnum = var2[var4];
            System.out.println(errorCodeEnum.errorCode + "         " + errorCodeEnum.errorMsg);
        }

    }
}