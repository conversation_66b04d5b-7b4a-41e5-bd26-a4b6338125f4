package com.ebon.energy.fms.mapper.second;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.domain.entity.HardwareModelAndFamilyDO;
import com.ebon.energy.fms.domain.entity.InverterExtDO;
import com.ebon.energy.fms.domain.entity.InvertersDO;
import com.ebon.energy.fms.domain.po.InvertersListPO;
import com.ebon.energy.fms.domain.po.SiteListPO;
import com.ebon.energy.fms.domain.vo.SiteVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface InvertersMapper extends BaseMapper<InvertersDO> {

    @Select({
            "<script>",
            "SELECT count(*) FROM Inverters i join fms.dbo.RedbackProductsView product WITH(NOLOCK) on i.SerialNumber=product.RedbackProductSn ",
            "<if test='po.edgeId != null and po.edgeId != \"\"'> left join (select SerialNumber,MAX(edgeId) AS edgeId from fms.dbo.DeviceView where ApplicationName='Ross' GROUP BY SerialNumber) d on d.SerialNumber=i.SerialNumber </if>",
            "<if test='po.isWatchdogOnline != null'> left join fms.dbo.WatchdogOnlineDeviceView wd on wd.SerialNumber=i.SerialNumber </if>",
            "<if test='(po.hasError != null and po.hasError == true) or (po.needAttention != null and po.needAttention == true)'>",
            " join (SELECT DISTINCT a.RedbackProductSn FROM fms.dbo.InverterAttention a",
            " CROSS APPLY OPENJSON(a.LastErrors) WITH (errorCode int '$.errorCode', firstUtc NVARCHAR(30) '$.firstUtc', latestUtc NVARCHAR(30) '$.latestUtc',",
            " description NVARCHAR(max) '$.description', errorDescription NVARCHAR(max) '$.errorDescription', errorCodeParsed NVARCHAR(max) '$.errorCodeParsed') AS error ",
            "where <![CDATA[ a.LastErrors IS NOT NULL and a.LastErrors <> '' ]]>",
            "<if test='po.hasError != null and po.hasError == true'>",
            " AND a.HasError = 1",
            "</if>",
            "<if test='po.needAttention != null and po.needAttention == true'>",
            " AND a.NeedAttention = 1",
            "</if>",
            "<if test='po.errorCode != null'>",
            " AND error.errorCode = #{po.errorCode} ",
            "</if>",
            "<if test='po.errorKeyword != null and po.errorKeyword != \"\"'>",
            " AND (<![CDATA[ error.description like CONCAT('%', #{po.errorKeyword}, '%') ESCAPE '!' or error.errorDescription like CONCAT('%', #{po.errorKeyword}, '%') ESCAPE '!' or error.errorCodeParsed like CONCAT('%', #{po.errorKeyword}, '%') ESCAPE '!' ]]>) ",
            "</if>",
            "<if test='po.errorStartTimeStr != null'>",
            " AND (<![CDATA[ error.firstUtc >= #{po.errorStartTimeStr} OR error.latestUtc >= #{po.errorStartTimeStr} ]]>)",
            "</if>",
            "<if test='po.errorEndTimeStr != null'>",
            " AND (<![CDATA[ error.firstUtc <= #{po.errorEndTimeStr} OR error.latestUtc <= #{po.errorEndTimeStr} ]]>)",
            "</if>",
            " ) ia",
            " on ia.RedbackProductSn = i.SerialNumber ",
            "</if>",
            "<where>",
            //<!-- serialNumber -->
            "<if test='po.serialNumber != null and po.serialNumber != \"\"'>",
            " AND i.SerialNumber LIKE CONCAT('%', #{po.serialNumber}, '%')",
            "</if>",

            //<!-- rossVersion -->
            "<if test='po.rossVersion != null and po.rossVersion != \"\"'>",
            " AND i.RossVersion LIKE CONCAT('%', #{po.rossVersion}, '%')",
            "</if>",

            //<!-- firmwareVersion -->
            "<if test='po.firmwareVersion != null and po.firmwareVersion != \"\"'>",
            " AND i.InverterFirmware LIKE CONCAT('%', #{po.firmwareVersion}, '%')",
            "</if>",

            //<!-- inverterModelName -->
            "<if test='po.inverterModelName != null and po.inverterModelName != \"\"'>",
            " AND (i.InverterModelName LIKE CONCAT('%', #{po.inverterModelName}, '%')",
            "<if test='modelNames != null and modelNames.size() > 0'>",
            " or i.InverterModelName IN",
            "<foreach item='modelName' collection='modelNames' open='(' separator=',' close=')'>",
            "#{modelName}",
            "</foreach>",
            "</if>",
            ") ",
            "</if>",

            //<!-- inverterMode -->
            "<if test='po.inverterMode != null and po.inverterMode != \"\"'>",
            " AND i.InverterMode LIKE CONCAT('%', REPLACE(#{po.inverterMode}, ' ', ''), '%')",
            "</if>",

            //<!-- installerCompany -->
            "<if test='po.installerCompany != null and po.installerCompany != \"\"'>",
            " AND i.MaintainingInstaller LIKE CONCAT('%', #{po.installerCompany}, '%')",
            "</if>",

            //<!-- watchdogDeviceId -->
            "<if test='po.watchdogDeviceId != null and po.watchdogDeviceId != \"\"'>",
            " AND i.watchdogDeviceId LIKE CONCAT('%', #{po.watchdogDeviceId}, '%')",
            "</if>",

            //<!-- edgeId -->
            "<if test='po.edgeId != null and po.edgeId != \"\"'>",
            " AND d.edgeId LIKE CONCAT('%', #{po.edgeId}, '%')",
            "</if>",

            //<!-- isOnline -->
            "<if test='po.isOnline != null'>",
            "<choose>",
            "<when test='po.isOnline == true'>",
            " AND <![CDATA[ product.LastSystemStatusReceived >= DATEADD(MINUTE, -10, GETDATE()) ]]> ",
            "</when>",
            "<otherwise>",
            " AND <![CDATA[ (product.LastSystemStatusReceived is null or product.LastSystemStatusReceived < DATEADD(MINUTE, -10, GETDATE())) ]]> ",
            "</otherwise>",
            "</choose>",
            "</if>",

            //<!-- isDailyOnline -->
            "<if test='po.isDailyOnline != null'>",
            " AND i.IsDailyOnline = #{po.isDailyOnline}",
            "</if>",

            //<!-- isHourlyOnline -->
            "<if test='po.isHourlyOnline != null'>",
            " AND i.IsHourlyOnline = #{po.isHourlyOnline}",
            "</if>",

            //<!-- isWatchdogOnline -->
            "<if test='po.isWatchdogOnline != null'>",
            "<choose>",
            "<when test='po.isWatchdogOnline == true'>",
            " AND wd.SerialNumber is not null ",
            "</when>",
            "<otherwise>",
            " AND wd.SerialNumber is null ",
            "</otherwise>",
            "</choose>",
            "</if>",

            //<!-- batteryStatus -->
            "<if test='po.batteryStatus != null'>",
            " AND ISJSON(product.LatestSystemStatus) = 1 AND JSON_VALUE(product.LatestSystemStatus, '$.Battery.Status') = #{po.batteryStatus} ",
            "</if>",

            //<!-- hasPredict -->
            "<if test='po.hasPredict != null'>",
            "<choose>",
            "<when test='po.hasPredict == true'>",
            " AND EXISTS (select RedbackProductSn from fms.dbo.ProductDailyForecast where RedbackProductSn=i.SerialNumber) ",
            "</when>",
            "<otherwise>",
            " AND NOT EXISTS (select RedbackProductSn from fms.dbo.ProductDailyForecast where RedbackProductSn=i.SerialNumber) ",
            "</otherwise>",
            "</choose>",
            "</if>",

            //<!-- tagIds -->
            "<if test='serialNumbers != null and serialNumbers.size() > 0'>",
            " AND i.SerialNumber IN",
            "<foreach item='sn' collection='serialNumbers' open='(' separator=',' close=')'>",
            "#{sn}",
            "</foreach>",
            "</if>",

            //<!-- hasBattery -->
            "<if test='po.hasBattery != null'>",
            "<choose>",
            "<when test='po.hasBattery == true'>",
            " AND (i.InverterModelName IS NULL OR i.InverterModelName NOT LIKE '%SI%')",
            " AND i.DetectedBatteryManufacturer IS NOT NULL",
            " AND i.DetectedBatteryManufacturer != 'None'",
            "</when>",
            "<otherwise>",
            " AND (i.InverterModelName LIKE '%SI%' OR i.DetectedBatteryManufacturer IS NULL OR i.DetectedBatteryManufacturer = 'None')",
            "</otherwise>",
            "</choose>",
            "</if>",
            "</where>",
            "</script>"
    })
    long countInverter(@Param("po") InvertersListPO po, @Param("serialNumbers") List<String> serialNumbers, @Param("modelNames") List<String> modelNames);

    @Select({
            "<script>",
            "SELECT i.*,",
            " CASE WHEN ISJSON(product.LatestSystemStatus)=1 ",
            "     THEN JSON_VALUE(product.LatestSystemStatus, '$.Battery.Status') ",
            "     ELSE NULL ",
            " END AS batteryStatus,",
            "IIF(product.LastSystemStatusReceived >= DATEADD(MINUTE, -10, GETDATE()), 1, 0) AS isOnline,",
            "IIF(wd.SerialNumber is null, 0, 1) as isWatchdogOnline,d.EdgeId",
            " FROM Inverters i join fms.dbo.RedbackProductsView product WITH(NOLOCK) on i.SerialNumber=product.RedbackProductSn ",
            " left join (select SerialNumber,MAX(edgeId) AS edgeId from fms.dbo.DeviceView where ApplicationName='Ross' GROUP BY SerialNumber) d on d.SerialNumber=i.SerialNumber ",
            " left join fms.dbo.WatchdogOnlineDeviceView wd on wd.SerialNumber=i.SerialNumber ",
            "<if test='(po.hasError != null and po.hasError == true) or (po.needAttention != null and po.needAttention == true)'>",
            " join (SELECT DISTINCT a.RedbackProductSn FROM fms.dbo.InverterAttention a",
            " CROSS APPLY OPENJSON(a.LastErrors) WITH (errorCode int '$.errorCode', firstUtc NVARCHAR(30) '$.firstUtc', latestUtc NVARCHAR(30) '$.latestUtc',",
            " description NVARCHAR(max) '$.description', errorDescription NVARCHAR(max) '$.errorDescription', errorCodeParsed NVARCHAR(max) '$.errorCodeParsed') AS error ",
            "where <![CDATA[ a.LastErrors IS NOT NULL and a.LastErrors <> '' ]]>",
            "<if test='po.hasError != null and po.hasError == true'>",
            " AND a.HasError = 1",
            "</if>",
            "<if test='po.needAttention != null and po.needAttention == true'>",
            " AND a.NeedAttention = 1",
            "</if>",
            "<if test='po.errorCode != null'>",
            " AND error.errorCode = #{po.errorCode} ",
            "</if>",
            "<if test='po.errorKeyword != null and po.errorKeyword != \"\"'>",
            " AND (<![CDATA[ error.description like CONCAT('%', #{po.errorKeyword}, '%') ESCAPE '!' or error.errorDescription like CONCAT('%', #{po.errorKeyword}, '%') ESCAPE '!' or error.errorCodeParsed like CONCAT('%', #{po.errorKeyword}, '%') ESCAPE '!' ]]>) ",
            "</if>",
            "<if test='po.errorStartTimeStr != null'>",
            " AND (<![CDATA[ error.firstUtc >= #{po.errorStartTimeStr} OR error.latestUtc >= #{po.errorStartTimeStr} ]]>)",
            "</if>",
            "<if test='po.errorEndTimeStr != null'>",
            " AND (<![CDATA[ error.firstUtc <= #{po.errorEndTimeStr} OR error.latestUtc <= #{po.errorEndTimeStr} ]]>)",
            "</if>",
            " ) ia",
            " on ia.RedbackProductSn = i.SerialNumber ",
            "</if>",
            "<where>",
            //<!-- serialNumber -->
            "<if test='po.serialNumber != null and po.serialNumber != \"\"'>",
            " AND i.SerialNumber LIKE CONCAT('%', #{po.serialNumber}, '%')",
            "</if>",

            //<!-- rossVersion -->
            "<if test='po.rossVersion != null and po.rossVersion != \"\"'>",
            " AND i.RossVersion LIKE CONCAT('%', #{po.rossVersion}, '%')",
            "</if>",

            //<!-- firmwareVersion -->
            "<if test='po.firmwareVersion != null and po.firmwareVersion != \"\"'>",
            " AND i.InverterFirmware LIKE CONCAT('%', #{po.firmwareVersion}, '%')",
            "</if>",

            //<!-- inverterModelName -->
            "<if test='po.inverterModelName != null and po.inverterModelName != \"\"'>",
            " AND (i.InverterModelName LIKE CONCAT('%', #{po.inverterModelName}, '%')",
            "<if test='modelNames != null and modelNames.size() > 0'>",
            " or i.InverterModelName IN",
            "<foreach item='modelName' collection='modelNames' open='(' separator=',' close=')'>",
            "#{modelName}",
            "</foreach>",
            "</if>",
            ") ",
            "</if>",

            //<!-- inverterMode -->
            "<if test='po.inverterMode != null and po.inverterMode != \"\"'>",
            " AND i.InverterMode LIKE CONCAT('%', REPLACE(#{po.inverterMode}, ' ', ''), '%')",
            "</if>",

            //<!-- installerCompany -->
            "<if test='po.installerCompany != null and po.installerCompany != \"\"'>",
            " AND i.MaintainingInstaller LIKE CONCAT('%', #{po.installerCompany}, '%')",
            "</if>",

            //<!-- watchdogDeviceId -->
            "<if test='po.watchdogDeviceId != null and po.watchdogDeviceId != \"\"'>",
            " AND i.watchdogDeviceId LIKE CONCAT('%', #{po.watchdogDeviceId}, '%')",
            "</if>",

            //<!-- edgeId -->
            "<if test='po.edgeId != null and po.edgeId != \"\"'>",
            " AND d.edgeId LIKE CONCAT('%', #{po.edgeId}, '%')",
            "</if>",

            //<!-- isOnline -->
            "<if test='po.isOnline != null'>",
            "<choose>",
            "<when test='po.isOnline == true'>",
            " AND <![CDATA[ product.LastSystemStatusReceived >= DATEADD(MINUTE, -10, GETDATE()) ]]> ",
            "</when>",
            "<otherwise>",
            " AND <![CDATA[ (product.LastSystemStatusReceived is null or product.LastSystemStatusReceived < DATEADD(MINUTE, -10, GETDATE())) ]]> ",
            "</otherwise>",
            "</choose>",
            "</if>",

            //<!-- isDailyOnline -->
            "<if test='po.isDailyOnline != null'>",
            " AND i.IsDailyOnline = #{po.isDailyOnline}",
            "</if>",

            //<!-- isHourlyOnline -->
            "<if test='po.isHourlyOnline != null'>",
            " AND i.IsHourlyOnline = #{po.isHourlyOnline}",
            "</if>",

            //<!-- isWatchdogOnline -->
            "<if test='po.isWatchdogOnline != null'>",
            "<choose>",
            "<when test='po.isWatchdogOnline == true'>",
            " AND wd.SerialNumber is not null ",
            "</when>",
            "<otherwise>",
            " AND wd.SerialNumber is null ",
            "</otherwise>",
            "</choose>",
            "</if>",
            
            //<!-- batteryStatus -->
            "<if test='po.batteryStatus != null'>",
            " AND ISJSON(product.LatestSystemStatus) = 1 AND JSON_VALUE(product.LatestSystemStatus, '$.Battery.Status') = #{po.batteryStatus} ",
            "</if>",

            //<!-- hasPredict -->
            "<if test='po.hasPredict != null'>",
            "<choose>",
            "<when test='po.hasPredict == true'>",
            " AND EXISTS (select RedbackProductSn from fms.dbo.ProductDailyForecast where RedbackProductSn=i.SerialNumber) ",
            "</when>",
            "<otherwise>",
            " AND NOT EXISTS (select RedbackProductSn from fms.dbo.ProductDailyForecast where RedbackProductSn=i.SerialNumber) ",
            "</otherwise>",
            "</choose>",
            "</if>",

            //<!-- tagIds -->
            "<if test='serialNumbers != null and serialNumbers.size() > 0'>",
            " AND i.SerialNumber IN",
            "<foreach item='sn' collection='serialNumbers' open='(' separator=',' close=')'>",
            "#{sn}",
            "</foreach>",
            "</if>",

            //<!-- hasBattery -->
            "<if test='po.hasBattery != null'>",
            "<choose>",
            "<when test='po.hasBattery == true'>",
            " AND (i.InverterModelName IS NULL OR i.InverterModelName NOT LIKE '%SI%')",
            " AND i.DetectedBatteryManufacturer IS NOT NULL",
            " AND i.DetectedBatteryManufacturer != 'None'",
            "</when>",
            "<otherwise>",
            " AND (i.InverterModelName LIKE '%SI%' OR i.DetectedBatteryManufacturer IS NULL OR i.DetectedBatteryManufacturer = 'None')",
            "</otherwise>",
            "</choose>",
            "</if>",
            "</where>",
            " ORDER BY product.LastSystemStatusReceived DESC",
            " OFFSET #{offSet} ROWS FETCH NEXT #{pageSize} ROWS ONLY",
            "</script>"
    })
    List<InverterExtDO> selectInverterPage(@Param("offSet") Long offSet, @Param("pageSize") Long pageSize, @Param("po") InvertersListPO po, @Param("serialNumbers") List<String> serialNumbers, @Param("modelNames") List<String> modelNames);
    
    @Select("select * from Inverters where ${query}")
    List<InvertersDO> selectInvertersByQuery(@Param("query") String query);

    @Update({"<script>",
            "UPDATE Inverters",
            "SET",
            "  IsHourlyOnline = CASE SerialNumber",
            "    <foreach collection=\"list\" item=\"item\" separator=\"\">",
            "      WHEN #{item.serialNumber} THEN #{item.isHourlyOnline}",
            "    </foreach>",
            "  END,",
            "  IsDailyOnline = CASE SerialNumber",
            "    <foreach collection=\"list\" item=\"item\" separator=\"\">",
            "      WHEN #{item.serialNumber} THEN #{item.isDailyOnline}",
            "    </foreach>",
            "  END",
            "WHERE SerialNumber IN",
            "  <foreach collection=\"list\" item=\"item\" open=\"(\" separator=\",\" close=\")\">",
            "    #{item.serialNumber}",
            "  </foreach>",
            "</script>"})
    int updateBatch(@Param("list") List<InvertersDO> list);
}
